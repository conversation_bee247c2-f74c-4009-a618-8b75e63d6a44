import { supabase } from '@/lib/supabase';
import { safeDbOperation } from '@/lib/errorHandling';

export interface InterviewFeedback {
  id: string;
  created_at: string;
  updated_at: string;
  interview_id: string;
  user_id: string;
  overall_rating: number;
  technical_rating: number | null;
  cultural_rating: number | null;
  strengths: string | null;
  weaknesses: string | null;
  notes: string | null;
  recommendation: 'strong_yes' | 'yes' | 'maybe' | 'no' | 'strong_no';
  // Enhanced fields
  user_name?: string;
  user_email?: string;
}

/**
 * Create interview feedback
 */
export const createInterviewFeedback = async (
  feedback: Omit<InterviewFeedback, 'id' | 'created_at' | 'updated_at' | 'user_name' | 'user_email'>
): Promise<InterviewFeedback | null> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('interview_feedback')
        .insert(feedback)
        .select(`
          *,
          profiles:user_id (
            name,
            email
          )
        `)
        .single();

      if (error) throw error;

      return {
        ...data,
        user_name: data.profiles?.name,
        user_email: data.profiles?.email
      };
    },
    'Failed to create interview feedback',
    null
  );
};

/**
 * Get feedback for an interview
 */
export const getInterviewFeedback = async (
  interviewId: string
): Promise<InterviewFeedback[]> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('interview_feedback')
        .select(`
          *,
          profiles:user_id (
            name,
            email
          )
        `)
        .eq('interview_id', interviewId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data.map(feedback => ({
        ...feedback,
        user_name: feedback.profiles?.name,
        user_email: feedback.profiles?.email
      }));
    },
    `Failed to fetch feedback for interview ${interviewId}`,
    []
  );
};

/**
 * Update interview feedback
 */
export const updateInterviewFeedback = async (
  feedbackId: string,
  updates: Partial<Omit<InterviewFeedback, 'id' | 'created_at' | 'updated_at' | 'interview_id' | 'user_id'>>
): Promise<InterviewFeedback | null> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('interview_feedback')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', feedbackId)
        .select(`
          *,
          profiles:user_id (
            name,
            email
          )
        `)
        .single();

      if (error) throw error;

      return {
        ...data,
        user_name: data.profiles?.name,
        user_email: data.profiles?.email
      };
    },
    'Failed to update interview feedback',
    null
  );
};

/**
 * Delete interview feedback
 */
export const deleteInterviewFeedback = async (
  feedbackId: string
): Promise<void> => {
  return safeDbOperation(
    async () => {
      const { error } = await supabase
        .from('interview_feedback')
        .delete()
        .eq('id', feedbackId);

      if (error) throw error;
    },
    'Failed to delete interview feedback'
  );
};

/**
 * Get feedback summary for an interview
 */
export const getInterviewFeedbackSummary = async (
  interviewId: string
): Promise<{
  averageOverallRating: number;
  averageTechnicalRating: number;
  averageCulturalRating: number;
  recommendationCounts: Record<string, number>;
  totalFeedbacks: number;
} | null> => {
  return safeDbOperation(
    async () => {
      const feedbacks = await getInterviewFeedback(interviewId);
      
      if (feedbacks.length === 0) {
        return {
          averageOverallRating: 0,
          averageTechnicalRating: 0,
          averageCulturalRating: 0,
          recommendationCounts: {},
          totalFeedbacks: 0
        };
      }

      const averageOverallRating = feedbacks.reduce((sum, f) => sum + f.overall_rating, 0) / feedbacks.length;
      
      const technicalRatings = feedbacks.filter(f => f.technical_rating !== null);
      const averageTechnicalRating = technicalRatings.length > 0 
        ? technicalRatings.reduce((sum, f) => sum + f.technical_rating!, 0) / technicalRatings.length 
        : 0;

      const culturalRatings = feedbacks.filter(f => f.cultural_rating !== null);
      const averageCulturalRating = culturalRatings.length > 0 
        ? culturalRatings.reduce((sum, f) => sum + f.cultural_rating!, 0) / culturalRatings.length 
        : 0;

      const recommendationCounts = feedbacks.reduce((counts, f) => {
        counts[f.recommendation] = (counts[f.recommendation] || 0) + 1;
        return counts;
      }, {} as Record<string, number>);

      return {
        averageOverallRating,
        averageTechnicalRating,
        averageCulturalRating,
        recommendationCounts,
        totalFeedbacks: feedbacks.length
      };
    },
    `Failed to get feedback summary for interview ${interviewId}`,
    null
  );
};