import { supabase } from '@/lib/supabase';
import { safeDbOperation } from '@/lib/errorHandling';

export interface InterviewParticipant {
  id: string;
  interview_id: string;
  user_id: string;
  role: 'interviewer' | 'candidate' | 'observer';
  status: 'invited' | 'confirmed' | 'declined' | 'no_response';
  created_at: string;
  updated_at: string;
  // Enhanced fields
  name?: string;
  email?: string;
}

/**
 * Add participants to an interview
 */
export const addInterviewParticipants = async (
  interviewId: string,
  participantUserIds: string[],
  role: 'interviewer' | 'observer' = 'interviewer'
): Promise<InterviewParticipant[]> => {
  return safeDbOperation(
    async () => {
      const participants = participantUserIds.map(userId => ({
        interview_id: interviewId,
        user_id: userId,
        role,
        status: 'invited' as const
      }));

      const { data, error } = await supabase
        .from('interview_participants')
        .insert(participants)
        .select();

      if (error) throw error;
      return data;
    },
    'Failed to add interview participants',
    []
  );
};

/**
 * Get participants for an interview
 */
export const getInterviewParticipants = async (
  interviewId: string
): Promise<InterviewParticipant[]> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('interview_participants')
        .select(`
          *,
          profiles:user_id (
            name,
            email
          )
        `)
        .eq('interview_id', interviewId);

      if (error) throw error;

      return data.map(participant => ({
        ...participant,
        name: participant.profiles?.name,
        email: participant.profiles?.email
      }));
    },
    `Failed to fetch participants for interview ${interviewId}`,
    []
  );
};

/**
 * Update participant status
 */
export const updateParticipantStatus = async (
  participantId: string,
  status: 'confirmed' | 'declined' | 'no_response'
): Promise<void> => {
  return safeDbOperation(
    async () => {
      const { error } = await supabase
        .from('interview_participants')
        .update({ status, updated_at: new Date().toISOString() })
        .eq('id', participantId);

      if (error) throw error;
    },
    'Failed to update participant status'
  );
};

/**
 * Remove participant from interview
 */
export const removeInterviewParticipant = async (
  participantId: string
): Promise<void> => {
  return safeDbOperation(
    async () => {
      const { error } = await supabase
        .from('interview_participants')
        .delete()
        .eq('id', participantId);

      if (error) throw error;
    },
    'Failed to remove interview participant'
  );
};