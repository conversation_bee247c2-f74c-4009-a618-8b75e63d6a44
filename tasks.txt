update logo with the new one

----------

email in interview email should be changed to the company that is sceduleing it. i think its working fine this part?

http://localhost:8080/dashboard/interviews/5eacb3fb-5e75-4d0d-a720-1e8c3638f04a, submit feedback open a model, but after i submit the data, does it even save to the db? because in http://localhost:8080/dashboard/interviews/5eacb3fb-5e75-4d0d-a720-1e8c3638f04a, i dont see ny data appear.
Does when scheudling an interview with someone, does the participant also receives email? why in http://localhost:8080/dashboard/interviews/5eacb3fb-5e75-4d0d-a720-1e8c3638f04a, i dont see the particpants appear at all?

-----------

update all name to Sourcio.ai instead of Sourcio.ai now. we no longer want to associate ourself with this name expert-recruiter.com or Sourcio.ai name at all. We have changed domain!

------------

interview,  email.... will update... 

----

recruiter role must have access to stuff

---


....

make the email editable.... for notif email.. let admin configure their email...

----------

they want to run as admin.. 

----------

interface, make it streamline...  job(should be easy to view canidate in jobs-wize, company...)

---

